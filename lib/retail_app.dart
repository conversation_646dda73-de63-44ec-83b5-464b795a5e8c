import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shop/app/account_statement/presentation/logic/bloc/account_statement/account_statement_cubit.dart';
import 'package:shop/app/account_statement/presentation/logic/bloc/account_statement/download_statement_cubit.dart';
import 'package:shop/app/account_statement/presentation/logic/bloc/account_statement/send_statement_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/company_reg_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/company_search_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/intercom_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/smile_job_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/smile_job_status_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/stripe_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/stripe_status_cubit.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/biller/presentation/logic/bloc/airtime_cubit.dart';
import 'package:shop/app/browse/presentation/logic/bloc/browse_collection_cubit.dart';
import 'package:shop/app/collections/presentation/logic/bloc/collection_cubit.dart';
import 'package:shop/app/collections/presentation/logic/bloc/variant_collection_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/account_cubit/account_cubit_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/customer_cubit/customer_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/customer_invoices_cubit/customer_invoices_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/invoice/invoice_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/item_cubit/item_cubit_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/selected_account_cubit.dart';
import 'package:shop/app/credit/presentation/logic/states/business_files_upload_state.dart';
import 'package:shop/app/credit/presentation/logic/states/business_reg_state.dart';
import 'package:shop/app/credit/presentation/logic/states/credit_docs_state.dart';
import 'package:shop/app/credit/presentation/logic/states/credit_request_state.dart';
import 'package:shop/app/homepage/presentation/logic/bloc/scroll_cubit.dart';
import 'package:shop/app/homepage/presentation/logic/utils/push_notification_status.dart';
import 'package:shop/app/homepage/presentation/ui/widgets/campaign/presentation/logic/campaign_cubit.dart';
import 'package:shop/app/loan/presentation/logic/bloc/base_loan_cubit.dart';
import 'package:shop/app/loan/presentation/logic/bloc/loan_cubit.dart';
import 'package:shop/app/loan/presentation/logic/bloc/loan_qualification_cubit.dart';
import 'package:shop/app/my_items/presentation/ui/logic/wishlist_cubit.dart';
import 'package:shop/app/notification/presentation/logic/bloc/bell_cubit.dart';
import 'package:shop/app/order/presentation/logic/bloc/order_cubit.dart';
import 'package:shop/app/pay/presentation/logic/agent_cubit.dart';
import 'package:shop/app/pay/presentation/logic/get_receipt_cubit.dart';
import 'package:shop/app/payments/presentation/logic/bloc/payment_bank_cubit.dart';
import 'package:shop/app/payments/presentation/logic/bloc/pos_transaction_cubit.dart';
import 'package:shop/app/payments/presentation/logic/bloc/wallet_transaction_cubit.dart';
import 'package:shop/app/product_search/presentation/logic/recently_viewed_cubit.dart';
import 'package:shop/app/product_search/presentation/logic/related_items_cubit.dart';
import 'package:shop/app/product_search/presentation/logic/search_bloc.dart';
import 'package:shop/app/promotion/presentation/logic/bloc/promotion_cubit.dart';
import 'package:shop/app/terminal/presentation/logic/cubits/terminals_balance_cubit.dart';
import 'package:shop/app/terminal/presentation/logic/cubits/terminals_cubit.dart';
import 'package:shop/app/terminal/presentation/logic/cubits/terminals_transactions_cubit.dart';
import 'package:shop/app/transactions/presentation/logic/bloc/invoice/inovice_cubit.dart';
import 'package:shop/app/transactions/presentation/logic/bloc/track_shipment/track_shipment_cubit.dart';
import 'package:shop/app/transactions/presentation/logic/bloc/transaction/transaction_cubit.dart';
import 'package:shop/app/transactions/presentation/logic/bloc/transactions/transactions_cubit.dart';
import 'package:shop/app/wallet_transfer/presentation/logic/beneficiary_state.dart';
import 'package:shop/app_config.dart';
import 'package:shop/src/components/src/app_link/link_bloc.dart';
import 'package:shop/src/components/src/blocs/order_search/bloc.dart';
import 'package:shop/src/components/src/blocs/sub_outlet.dart';
import 'package:shop/src/components/src/forced_update/forced_update.dart';
import 'package:shop/src/components/src/utils/browser_detect/io.dart'
    if (dart.library.html) 'package:shop/src/components/src/utils/browser_detect/html.dart';
import 'package:shop/src/components/src/utils/error_handler.dart';
import 'package:shop/src/go_router/router.dart';
import 'package:shop/src/res/themes/theme.dart';
import 'package:shop/src/services/session_info.dart';
import 'package:shop/td10n/app_localizations.dart';
import 'package:shop/td10n/td10n.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_commons_flutter/models/variant_inventory.dart';
import 'package:td_flutter_core/config/DI/di.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

import 'app/authentication/presentation/logic/bloc/preferences_cubit.dart';
import 'app/biller/presentation/logic/fetch_receipt_cubit.dart';
import 'app/collections/presentation/logic/bloc/new_items_cubit.dart';
import 'app/deals/presentation/logic/cubit/deals_cubit.dart';
import 'app/edit_profile/presentation/logic/profile_image_cubit.dart';
import 'app/homepage/presentation/logic/bloc/brands_cubit.dart';
import 'app/homepage/presentation/logic/bloc/manager_cubit.dart';
import 'app/homepage/presentation/logic/bloc/service_option_cubit.dart';
import 'app/homepage/presentation/logic/utils/referee_code.dart';
import 'app/loan/presentation/logic/bloc/kyc_cubit.dart';
import 'app/loan/presentation/logic/bloc/loan_repayment_cubit.dart';
import 'app/loan/presentation/logic/bloc/upcoming_payment_cubit.dart';
import 'app/my_cart/presentation/logic/bloc/cart_cubit.dart';
import 'app/my_cart/presentation/logic/bloc/order_preview_cubit.dart';
import 'app/my_cart/presentation/logic/bloc/popular_items_cubit.dart';
import 'app/my_cart/presentation/logic/utils/quick_buy_notifier.dart';
import 'app/pay/presentation/logic/charge_cubit.dart';
import 'app/wallet/presentation/logic/bloc/market_locator_cubit.dart';
import 'src/components/src/app_notification/push_notification_bloc.dart';

class RetailApp extends StatefulWidget {
  final AppConfig config;

  const RetailApp({super.key, required this.config});

  @override
  _RetailAppState createState() => _RetailAppState();
}

class _RetailAppState extends State<RetailApp> {
  late final UserCubit _userCubit;
  late final CartCubit _cartCubit;
  late final SubOutletBloc _subOutletBloc;
  late final CollectionCubit _collectionCubit;
  late final LoanCubit _loanCubit;
  late LoanRepaymentCubit _loanRepaymentCubit;
  late final KycCubit _kycCubit;
  late final BusinessRegCubit _businessRegCubit;
  late final IntercomCubit _intercomCubit;
  late final CompanyRegistrationCubit _companyRegCubit;
  late final CompanySearchCubit _companySearchCubit;
  late final BrowseCollectionCubit _browseCollectionCubit;
  late RelatedItemsCubit _relatedItemsCubit;
  late BellCubit _bellBloc;
  // late final BetaLevelsCubit _betaLevelsCubit;
  // late final CardCubit _cardCubit;
  late VariantCollectionCubit _variantCollectionCubit;
  late final LoanQualificationCubit _loanQualificationCubit;
  late final OrderPreviewCubit _orderPreviewCubit;
  late final LinkBloc _linkBloc;
  late final CampaignCubit _campaignCubit;
  late final WishlistCubit _wishlistCubit;
  late final UpdateCubit _updateCubit;
  late final SmileJobCubit _smileJobCubit;
  late final StripeCubit _stripeJobCubit;
  late final StripeStatusCubit _stripeStatusCubit;
  late final SmileJobStatusCubit _smileJobStatusCubit;
  late final PromotionCubit _promotionCubit;
  late final BaseLoanCubit _baseLoanCubit;
  late final MarketLocatorCubit _marketLocatorCubit;
  late final NewItemsCubit _newItemsCubit;
  late final PushNotificationBloc _pushNotificationBloc;
  late final OrderCubit _orderCubit;
  late final ProfileImageCubit _profileImageCubit;
  late final PopularItemsCubit _popularItemsCubit;
  late final ManagerCubit _managerCubit;
  late final AirtimeCubit _airtimeCubit;
  late final ServiceOptionCubit _serviceOptionCubit;
  late final UpcomingPaymentCubit _upcomingPaymentCubit;
  late final DealsCubit _dealsCubit;
  late final BrandsCubit _brandsCubit;
  late final PreferencesCubit _preferencesCubit;
  late final PaymentBankCubit _paymentBankCubit;
  late final BeneficiaryState _beneficiaryCubit;
  late final TerminalsCubit _terminalsCubit;
  late final CustomerCubit _customerCubit;
  late final ItemCubit _itemCubit;
  late final AccountCubit _accountCubit;
  late final InvoiceViewerCubit _invoiceViewerCubit;
  late final CustomerInvoicesCubit _customerInvoicesCubit;

  late final RecentlyViewedCubit _recentlyViewedCubit;
  final PopularItemsCache _popularItemsCache = PopularItemsCache();
  final OrdersSearchCache _ordersSearchCache = OrdersSearchCache();
  final VariantCollectionCache _variantCollectionCache =
      VariantCollectionCache();
  final VariantInventoryCache _variantInventoryCache = VariantInventoryCache();

  final _quickBuyNotifier = QuickBuyNotifier();
  final _refereeProvider = RefereeProvider();
  final _pushNotificationStatus = PushNotificationStatus();
  late final storage = SharedPreferences.getInstance();
  final AppRouter appRouter = AppRouter();
  bool darkThemeEnabled = false;
  // final appRouter = locator<AppRouter>();

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      removeDocument('loading-indicator');
    });
    _createBloc();
    _initSession();
    darkThemeEnabled = widget.config.darkThemeEnabled ?? false;
  }

  void _initSession() {
    appRouter.initSession = SessionInfo(_userCubit.currentUserStream);
  }

  @override
  void dispose() {
    _disposeBloc();
    super.dispose();
  }

  void _disposeBloc() {
    _recentlyViewedCubit.close();
    _userCubit.dispose();
    _cartCubit.close();
    _subOutletBloc.dispose();
    _collectionCubit.close();
    _loanCubit.close();
    _loanRepaymentCubit.close();
    _kycCubit.close();
    _businessRegCubit.close();
    _browseCollectionCubit.close();
    _relatedItemsCubit.close();
    _bellBloc.close();
    _terminalsCubit.close();
    // _betaLevelsCubit.close();
    // _cardCubit.close();
    _variantCollectionCubit.close();
    _loanQualificationCubit.dispose();
    _orderPreviewCubit.close();
    _linkBloc.close();
    _updateCubit.close();
    _promotionCubit.close();
    _baseLoanCubit.close();
    _marketLocatorCubit.close();
    _newItemsCubit.close();
    _orderCubit.close();
    _profileImageCubit.close();
    _popularItemsCubit.close();
    _managerCubit.close();
    _airtimeCubit.close();
    _serviceOptionCubit.close();
    _serviceOptionCubit.close();
    _upcomingPaymentCubit.close();
    _dealsCubit.close();
    _brandsCubit.close();
    _preferencesCubit.close();
    _paymentBankCubit.close();
    _beneficiaryCubit.close();
    _companyRegCubit.close();
    _companySearchCubit.close();
    _stripeJobCubit.close();
    _stripeStatusCubit.close();
    _customerCubit.close();
    _itemCubit.close();
    _accountCubit.close();
    _invoiceViewerCubit.close();
    _customerInvoicesCubit.close();
  }

  void _createBloc() {
    _userCubit = UserCubit(appRouter);
    _cartCubit = CartCubit();
    _subOutletBloc = SubOutletBloc(outlet: _userCubit.currentOutletStream);
    _collectionCubit = CollectionCubit(locator());
    _loanCubit = LoanCubit(locator());
    _loanRepaymentCubit = LoanRepaymentCubit(locator());
    _businessRegCubit = BusinessRegCubit(BusinessRegInitial());
    _smileJobCubit = SmileJobCubit(locator(), locator(), locator());
    _stripeJobCubit = StripeCubit(locator());
    _stripeStatusCubit = StripeStatusCubit(locator());
    _smileJobStatusCubit = SmileJobStatusCubit(locator());
    _intercomCubit = IntercomCubit(config: widget.config);
    _companyRegCubit = CompanyRegistrationCubit(locator());
    _companySearchCubit = CompanySearchCubit(locator());
    _browseCollectionCubit = BrowseCollectionCubit(locator());
    _relatedItemsCubit = RelatedItemsCubit(locator());
    _bellBloc = BellCubit();
    _kycCubit = KycCubit(_smileJobStatusCubit, _stripeStatusCubit);
    // _betaLevelsCubit = BetaLevelsCubit(locator(), locator(), locator());
    // _cardCubit = CardCubit(locator());
    _variantCollectionCubit =
        VariantCollectionCubit(locator(), locator(), _variantCollectionCache);
    _loanQualificationCubit = LoanQualificationCubit(locator(), locator());
    _orderPreviewCubit = OrderPreviewCubit();
    _linkBloc = LinkBloc(appRouter);
    _campaignCubit = CampaignCubit();
    _wishlistCubit = WishlistCubit();
    _updateCubit = UpdateCubit(locator());
    _promotionCubit = PromotionCubit(locator());
    _baseLoanCubit = BaseLoanCubit(locator());
    _marketLocatorCubit = MarketLocatorCubit(locator());
    _newItemsCubit = NewItemsCubit(locator());
    _pushNotificationBloc = PushNotificationBloc(appRouter);
    _orderCubit = OrderCubit(locator(), locator());
    _profileImageCubit = ProfileImageCubit(locator(), locator());
    _popularItemsCubit = PopularItemsCubit();
    _managerCubit = ManagerCubit();
    _airtimeCubit = AirtimeCubit();
    _serviceOptionCubit = ServiceOptionCubit();
    _upcomingPaymentCubit = UpcomingPaymentCubit(locator());
    _dealsCubit = DealsCubit(locator());
    _recentlyViewedCubit = RecentlyViewedCubit();
    _brandsCubit = BrandsCubit(locator());
    _preferencesCubit = PreferencesCubit(locator());
    _paymentBankCubit = PaymentBankCubit(locator());
    _beneficiaryCubit = BeneficiaryState(locator());
    _terminalsCubit = TerminalsCubit(locator());
    _customerCubit = CustomerCubit(locator(), locator(), locator());
    _itemCubit = ItemCubit(locator(), locator());
    _accountCubit = AccountCubit(locator(), locator());
    _invoiceViewerCubit = InvoiceViewerCubit(locator());
    _customerInvoicesCubit = CustomerInvoicesCubit(locator(), locator());

    _userCubit.registerListeners([
      _baseLoanCubit,
      _cartCubit,
      _intercomCubit,
      // _betaLevelsCubit,
      _managerCubit,
      // _cardCubit,
      _campaignCubit,
      _wishlistCubit,
      _browseCollectionCubit,
      _collectionCubit,
      _businessRegCubit,
      _kycCubit,
      _loanCubit,
      _bellBloc,
      _loanQualificationCubit,
      _updateCubit,
      _promotionCubit,
      _marketLocatorCubit,
      _pushNotificationBloc,
      _orderCubit,
      _profileImageCubit,
      _airtimeCubit,
      _serviceOptionCubit,
      _upcomingPaymentCubit,
      _linkBloc,
      _dealsCubit,
      _recentlyViewedCubit,
      _quickBuyNotifier,
      _refereeProvider,
      _brandsCubit,
      _companyRegCubit,
      _companySearchCubit,
      _stripeJobCubit,
      _stripeStatusCubit,
      _preferencesCubit,
      _paymentBankCubit,
      _beneficiaryCubit,
      _variantCollectionCubit,
      _terminalsCubit,
      _pushNotificationStatus,
      _customerCubit,
      _itemCubit,
      _accountCubit,
      _invoiceViewerCubit,
      _customerInvoicesCubit,
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<AppConfig>.value(value: widget.config),
        Provider<UserCubit>.value(value: _userCubit),
        Provider<PopularItemsCache>.value(value: _popularItemsCache),
        Provider<OrdersSearchCache>.value(value: _ordersSearchCache),
        Provider<VariantInventoryCache>.value(value: _variantInventoryCache),
        ChangeNotifierProvider<CartCubit>.value(value: _cartCubit),
        Provider<SubOutletBloc>.value(value: _subOutletBloc),
        Provider<VariantCollectionCache>.value(value: _variantCollectionCache),
        Provider<OrderPreviewCubit>.value(value: _orderPreviewCubit),
        Provider<LinkBloc>.value(value: _linkBloc),
        Provider<OrderCubit>.value(value: _orderCubit),
        ChangeNotifierProvider<QuickBuyNotifier>(
            create: (_) => _quickBuyNotifier),
        ChangeNotifierProvider<RefereeProvider>(
            create: (_) => _refereeProvider),
        ChangeNotifierProvider<PushNotificationStatus>(
            create: (_) => _pushNotificationStatus),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider<SmileJobCubit>.value(value: _smileJobCubit),
          BlocProvider<StripeCubit>.value(value: _stripeJobCubit),
          BlocProvider<CompanyRegistrationCubit>.value(value: _companyRegCubit),
          BlocProvider<CompanySearchCubit>.value(value: _companySearchCubit),
          BlocProvider<StripeStatusCubit>.value(value: _stripeStatusCubit),
          BlocProvider<SmileJobStatusCubit>.value(value: _smileJobStatusCubit),
          BlocProvider<WishlistCubit>.value(value: _wishlistCubit),
          BlocProvider<CollectionCubit>.value(value: _collectionCubit),
          BlocProvider<TerminalsCubit>.value(value: _terminalsCubit),
          BlocProvider<LoanCubit>.value(value: _loanCubit),
          BlocProvider<LoanRepaymentCubit>.value(value: _loanRepaymentCubit),
          BlocProvider<KycCubit>.value(value: _kycCubit),
          BlocProvider<BusinessRegCubit>.value(value: _businessRegCubit),
          BlocProvider<BrowseCollectionCubit>.value(
              value: _browseCollectionCubit),
          BlocProvider<RelatedItemsCubit>.value(value: _relatedItemsCubit),
          BlocProvider<BellCubit>.value(value: _bellBloc),
          // BlocProvider<BetaLevelsCubit>.value(value: _betaLevelsCubit),
          // BlocProvider<CardCubit>.value(value: _cardCubit),
          BlocProvider<CampaignCubit>.value(value: _campaignCubit),
          BlocProvider<VariantCollectionCubit>.value(
              value: _variantCollectionCubit),
          BlocProvider<LoanQualificationCubit>.value(
              value: _loanQualificationCubit),
          BlocProvider<UpdateCubit>.value(value: _updateCubit),
          BlocProvider<PromotionCubit>.value(value: _promotionCubit),
          BlocProvider<BaseLoanCubit>.value(value: _baseLoanCubit),
          BlocProvider<MarketLocatorCubit>.value(value: _marketLocatorCubit),
          BlocProvider<ProfileImageCubit>.value(value: _profileImageCubit),
          BlocProvider<NewItemsCubit>.value(value: _newItemsCubit),
          BlocProvider<PopularItemsCubit>.value(value: _popularItemsCubit),
          BlocProvider<ManagerCubit>.value(value: _managerCubit),
          BlocProvider<AirtimeCubit>.value(value: _airtimeCubit),
          BlocProvider<RecentlyViewedCubit>.value(value: _recentlyViewedCubit),
          BlocProvider<ServiceOptionCubit>.value(value: _serviceOptionCubit),
          BlocProvider<UpcomingPaymentCubit>.value(
              value: _upcomingPaymentCubit),
          BlocProvider<DealsCubit>.value(value: _dealsCubit),
          BlocProvider<BrandsCubit>.value(value: _brandsCubit),
          BlocProvider<PreferencesCubit>.value(value: _preferencesCubit),
          BlocProvider.value(value: _customerCubit),
          BlocProvider.value(value: _itemCubit),
          BlocProvider.value(value: _accountCubit),
          BlocProvider.value(value: _invoiceViewerCubit),
          BlocProvider<AgentCubit>(
            create: (BuildContext context) => AgentCubit(locator()),
          ),
          BlocProvider<ScrollCubit>(
            create: (BuildContext context) => ScrollCubit(),
          ),
          BlocProvider<ChargeCubit>(
            create: (BuildContext context) => ChargeCubit(locator()),
          ),
          BlocProvider<GetReceiptCubit>(
            create: (BuildContext context) => GetReceiptCubit(locator()),
          ),
          BlocProvider(
            create: (BuildContext context) => FetchReceiptCubit(locator()),
          ),
          BlocProvider(
            create: (BuildContext context) => WalletTransactionCubit(locator()),
          ),
          BlocProvider(
            create: (BuildContext context) => PosTransactionCubit(locator()),
          ),
          BlocProvider<BeneficiaryState>.value(value: _beneficiaryCubit),
          BlocProvider<PaymentBankCubit>.value(value: _paymentBankCubit),
          BlocProvider<CustomerInvoicesCubit>.value(
              value: _customerInvoicesCubit),
          BlocProvider(
            create: (BuildContext context) => CreditRequestState(locator()),
            lazy: false,
          ),
          BlocProvider(
            create: (BuildContext context) => CreditDocsState(locator()),
          ),
          BlocProvider(
            create: (BuildContext context) => BusinessFilesUploadState(
              locator(),
            ),
          ),
          BlocProvider(
            create: (BuildContext context) =>
                TerminalsTransactionCubit(locator()),
          ),
          BlocProvider(
            create: (BuildContext context) => TerminalsBalanceCubit(locator()),
          ),
          BlocProvider(
            create: (context) => TransactionsCubit(
              locator(),
              locator(),
            ),
          ),
          BlocProvider(
            create: (context) => TransactionCubit(locator()),
          ),

          BlocProvider(
            create: (context) => InvoiceCubit(
              locator(),
            ),
          ),

          BlocProvider(
            create: (context) => TrackShipmentCubit(locator()),
          ),

          BlocProvider(
            create: (context) => AccountStatementCubit(
              locator(),
            ),
          ),

          BlocProvider(
            create: (context) => DownloadStatementCubit(
              locator(),
            ),
          ),

          BlocProvider(
            create: (context) => SendStatementCubit(
              locator(),
            ),
          ),

          BlocProvider(
            create: (context) => SelectedAccountCubit(),
          ),
        ],
        child: ScaleAware(
          config: const ScaleConfig(
            allowFontScaling: true,
            width: 411,
            height: 823,
          ),
          child: GestureDetector(
            onTap: () {
              // un-focus input on tap out
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
                currentFocus.requestFocus(FocusNode());
              }
            },
            child: MaterialApp.router(
              title: 'TradeDepot',
              debugShowCheckedModeBanner: false,
              restorationScopeId: 'app',
              supportedLocales: TD10n.all,
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
              ],
              theme: tdShopTheme(context),
              darkTheme: darkThemeEnabled
                  ? tdShopTheme(context, brightness: Brightness.dark)
                  : null,
              backButtonDispatcher: appRouter.router.backButtonDispatcher,
              routerDelegate: appRouter.router.routerDelegate,
              routeInformationProvider:
                  appRouter.router.routeInformationProvider,
              routeInformationParser: appRouter.router.routeInformationParser,
              builder: (context, child) {
                return MediaQueryHandler(child: child);
                // final data = MediaQuery.of(context);
                // final app = MediaQuery(
                //   data: data.copyWith(textScaler: TextScaler.linear(1)),
                //   child: child ?? const SizedBox.shrink(),
                // );

                // return app;
              },
            ),
          ),
        ),
      ),
    );
  }
}

class MediaQueryHandler extends StatefulWidget {
  final Widget? child;

  const MediaQueryHandler({super.key, this.child});

  @override
  MediaQueryHandlerState createState() => MediaQueryHandlerState();
}

class MediaQueryHandlerState extends State<MediaQueryHandler> {
  @override
  Widget build(BuildContext context) {
    final data = MediaQuery.of(context);
    final app = MediaQuery(
      data: data.copyWith(textScaler: const TextScaler.linear(1)),
      child: widget.child ?? const SizedBox.shrink(),
    );

    ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
      ErrorHandler.report(errorDetails.exception, errorDetails.stack,
          env: config.environment);
      return app;
    };

    return app;
  }
}
