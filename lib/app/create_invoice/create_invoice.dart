import 'package:shop/app/create_invoice/data/data_source/create_invoice_data_source.dart';
import 'package:shop/app/create_invoice/data/data_source/create_invoice_data_source_impl.dart';
import 'package:shop/app/create_invoice/data/repo_impl/create_invoice_repo_impl.dart';
import 'package:shop/app/create_invoice/domain/repo/create_invoice_repo.dart';
import 'package:shop/app/create_invoice/domain/use_case/add_customer.dart';
import 'package:shop/app/create_invoice/domain/use_case/add_item.dart';
import 'package:shop/app/create_invoice/domain/use_case/create_invoice.dart';
import 'package:shop/app/create_invoice/domain/use_case/delete_customer_invoice.dart';
import 'package:shop/app/create_invoice/domain/use_case/fetch_customer_invoices.dart';
import 'package:shop/app/create_invoice/domain/use_case/fetch_my_customers.dart';
import 'package:shop/app/create_invoice/domain/use_case/get_bank_account.dart';
import 'package:shop/app/create_invoice/domain/use_case/get_banks.dart';
import 'package:shop/app/create_invoice/domain/use_case/get_invoice.dart';
import 'package:shop/app/create_invoice/domain/use_case/get_items.dart';
import 'package:shop/app/create_invoice/domain/use_case/link_account.dart';
import 'package:shop/app/create_invoice/domain/use_case/search_invoice_items.dart';
import 'package:shop/app/create_invoice/domain/use_case/update_customer.dart';
import 'package:shop/app/create_invoice/domain/use_case/validate_account.dart';
import 'package:td_commons_flutter/app_config.dart';
import 'package:td_flutter_core/config/DI/di.dart';

void registerCreateInvoiceDependencies(AppConfig config) {
  // Data sources
  locator.registerLazySingleton<CreateInvoiceDataSource>(
    () => CreateInvoiceDataSourceImpl(
      locator(),
      config.firebaseServiceUrl!,
    ),
  );

  // Repositories
  locator.registerLazySingleton<CreateInvoiceRepo>(
    () => CreateInvoiceRepoImpl(locator()),
  );

  locator.registerLazySingleton(() => AddCustomer(locator()));
  locator.registerLazySingleton(() => UpdateCustomer(locator()));

  locator.registerLazySingleton(() => AddItem(locator()));

  locator.registerLazySingleton(() => CreateInvoiceUseCase(locator()));

  locator.registerLazySingleton(() => FetchMyCustomers(locator()));

  locator.registerLazySingleton(() => GetBankAccounts(locator()));

  locator.registerLazySingleton(() => GetBanks(locator()));

  locator.registerLazySingleton(() => ValidateAccount(locator()));

  locator.registerLazySingleton(() => LinkAccount(locator()));

  locator.registerLazySingleton(() => GetItem(locator()));

  locator.registerLazySingleton(() => GetInvoice(locator()));

  locator.registerLazySingleton(() => FetchCustomerInvoices(locator()));

  locator.registerLazySingleton(() => DeleteCustomerInvoice(locator()));

  locator.registerLazySingleton(() => SearchInvoiceItems(locator()));
}
