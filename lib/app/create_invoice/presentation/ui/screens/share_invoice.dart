import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pdfx/pdfx.dart';
import 'package:share_plus/share_plus.dart';
import 'package:share_whatsapp_plus/share_whatsapp_plus.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/presentation/logic/invoice/invoice_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/selected_account_cubit.dart';
import 'package:shop/app/create_invoice/presentation/ui/screens/full_screen_invoice_preview.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/colors.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/share_option_tile.dart';
import 'package:shop/app/transactions/data/models/invoice_data.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/widgets/currency_item/currency_item.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:td_flutter_src/scaler/scaler.dart';

class ShareInvoiceScreen extends StatefulWidget {
  const ShareInvoiceScreen(this.args, {super.key});

  final ShareInvoiceScreenArgs args;

  @override
  State<ShareInvoiceScreen> createState() => _ShareInvoiceScreenState();
}

class _ShareInvoiceScreenState extends State<ShareInvoiceScreen> {
  late InvoiceViewerCubit cubit;
  bool whatsappInstalled = false;

  late final invoice = widget.args.invoice;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    cubit = context.read();
    cubit.getInvoice(invoice.id);
    super.initState();
    _check();
  }

  Future _check() async {
    final val1 = await shareWhatsappPlus.installed(type: WhatsApp.standard);
    final val2 = await shareWhatsappPlus.installed(type: WhatsApp.business);
    whatsappInstalled = val1 || val2;

    if (mounted) {
      setState(() {
        whatsappInstalled = val1 || val2;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (_, __) async {
        if (widget.args.resetInvoiceFields) {
          resetCreateInvoiceFields(context);
        }
      },
      child: Scaffold(
        backgroundColor: backgroundColor,
        appBar: ShopAppBar.invoiceAppBar(
          context,
          title: 'Share Invoice',
          titleStyle: textTheme.titleLarge
              ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
          centerTitle: false,
        ),
        body: Padding(
          padding: const EdgeInsets.all(16),
          child: BlocBuilder<InvoiceViewerCubit, InvoiceState>(
            builder: (context, state) {
              Widget child = const Offstage();

              if (state is InvoiceLoading) {
                child = const Center(
                  child: CircularProgressIndicator(),
                );
              }
              if (state is InvoiceError) {
                child = Center(
                  child: KErrorScreen(
                    state.error,
                    () => cubit.getInvoice(invoice.id),
                  ),
                );
              }
              if (state is InvoiceLoaded) {
                child = SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              offset: const Offset(0, 1),
                              blurRadius: 2,
                              color: Colors.black.withValues(alpha: 0.05),
                            ),
                          ],
                        ),
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildInvoiceDetails(),
                            const YMargin(16),
                            const Divider(
                              height: 1,
                              color: Color(0xFFF3F4F6),
                            ),
                            const YMargin(19),
                            _buildToSection(),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      _buildPreviewSection(state.invoiceFile),
                      const SizedBox(height: 24),
                      _buildShareOptions(state.invoiceFile),
                      // Add bottom padding for better scrolling experience
                      const SizedBox(height: 24),
                    ],
                  ),
                );
              }

              return AnimatedSwitcher(
                duration: kThemeAnimationDuration,
                child: child,
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildInvoiceDetails() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice #',
              style: textTheme.bodyMedium?.copyWith(
                color: const Color(0xFF6B7280),
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              invoice.invoiceNumber?.toString() ?? '',
              style: textTheme.bodyLarge?.copyWith(
                color: const Color(0xFF1F2937),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'Amount',
              style: textTheme.bodyMedium?.copyWith(
                color: const Color(0xFF6B7280),
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              CurrencyItem.value(context, invoice.total ?? 0,
                  invoice.currency?.iso ?? UserCubit.instance!.currencyCode),
              style: textTheme.bodyLarge?.copyWith(
                color: const Color(0xFF1F2937),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildToSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'To',
          style: textTheme.bodyMedium?.copyWith(
            color: const Color(0xFF6B7280),
            fontWeight: FontWeight.w600,
          ),
        ),
        const YMargin(8),
        Text(
          invoice.customerName ?? '',
          style: textTheme.bodyLarge?.copyWith(
            color: const Color(0xFF1F2937),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewSection(InvoiceFile invoiceFile) {
    return GestureDetector(
      onTap: () {
        if (invoiceFile.file?.path != null) {
          Navigator.of(context).push(MaterialPageRoute(
            builder: (_) => FullScreenInvoicePreview(
              filePath: invoiceFile.file!.path,
            ),
          ));
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 20,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 1),
              blurRadius: 2,
              color: Colors.black.withValues(alpha: 0.05),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Preview',
              style: textTheme.bodyLarge?.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2937),
              ),
            ),
            const YMargin(16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: const Color(0xFFF3F4F6),
                borderRadius: BorderRadius.circular(12),
              ),
              child: AbsorbPointer(
                child: PdfViewPinch(
                  key: UniqueKey(),
                  controller: PdfControllerPinch(
                    document: PdfDocument.openFile(
                      invoiceFile.file?.path ?? '',
                    ),
                  ),
                  padding: 0.1,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareOptions(InvoiceFile invoiceFile) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 20,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, 1),
            blurRadius: 2,
            color: Colors.black.withValues(alpha: 0.05),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Share via',
            style: textTheme.bodyLarge?.copyWith(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          if (whatsappInstalled) ...[
            const YMargin(16),
            ShareOptionTile(
              icon: 'assets/svgs/invoice/whatsapp.svg',
              title: 'WhatsApp',
              onTap: () {
                // final fileUri = Uri.parse(invoiceFile.invoiceUrl!);
                // final uri = "whatsapp://send?text=$fileUri";
                // openUri(uri);
                final xFile = XFile(invoiceFile.file!.path);
                shareWhatsappPlus.shareFile(xFile);
              },
            ),
          ],
          // YMargin(16),
          // ShareOptionTile(
          //   icon: 'assets/svgs/invoice/email.svg',
          //   title: 'Email',
          //   onTap: () {
          //     final subject = Uri.encodeComponent("Here is your file");
          //     final body = Uri.encodeComponent(
          //         "Please see the attached file: $filePath");
          //     final mailtoUrl = "mailto:?subject=$subject&body=$body";
          //     openUri(mailtoUrl);
          //   },
          // ),
          const YMargin(16),
          ShareOptionTile(
            icon: 'assets/svgs/invoice/copy.svg',
            title: 'Share',
            onTap: () {
              final RenderBox box = context.findRenderObject() as RenderBox;
              Share.shareXFiles([
                XFile(
                  invoiceFile.file?.path ?? '',
                  mimeType: 'application/pdf',
                )
              ],
                  subject: 'Invoice',
                  sharePositionOrigin:
                      box.localToGlobal(Offset.zero) & box.size);
            },
          ),
        ],
      ),
    );
  }
}

class ShareInvoiceScreenArgs {
  final CustomerInvoice invoice;
  final bool resetInvoiceFields;
  ShareInvoiceScreenArgs({
    required this.invoice,
    this.resetInvoiceFields = true,
  });
}
