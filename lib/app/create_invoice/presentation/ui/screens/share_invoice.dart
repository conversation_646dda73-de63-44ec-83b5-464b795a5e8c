import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:pdfx/pdfx.dart';
import 'package:share_plus/share_plus.dart';
import 'package:share_whatsapp_plus/share_whatsapp_plus.dart';
import 'package:shop/app/create_invoice/data/model/customer_invoice.dart';
import 'package:shop/app/create_invoice/presentation/logic/invoice/invoice_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/selected_account_cubit.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/colors.dart';
import 'package:shop/app/transactions/data/models/invoice_data.dart';
import 'package:shop/src/components/src/error/error_widget.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';

class ShareInvoiceScreen extends StatefulWidget {
  const ShareInvoiceScreen(this.args, {super.key});

  final ShareInvoiceScreenArgs args;

  @override
  State<ShareInvoiceScreen> createState() => _ShareInvoiceScreenState();
}

class _ShareInvoiceScreenState extends State<ShareInvoiceScreen> {
  late InvoiceViewerCubit cubit;
  bool whatsappInstalled = false;
  PdfController? _pdfController;

  late final invoice = widget.args.invoice;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  void initState() {
    cubit = context.read();
    cubit.getInvoice(invoice.id);
    super.initState();
    _check();
  }

  Future _check() async {
    final val1 = await shareWhatsappPlus.installed(type: WhatsApp.standard);
    final val2 = await shareWhatsappPlus.installed(type: WhatsApp.business);
    whatsappInstalled = val1 || val2;

    if (mounted) {
      setState(() {
        whatsappInstalled = val1 || val2;
      });
    }
  }

  @override
  void dispose() {
    _pdfController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (_, __) async {
        if (widget.args.resetInvoiceFields) {
          resetCreateInvoiceFields(context);
        }
      },
      child: Scaffold(
        backgroundColor: backgroundColor,
        appBar: ShopAppBar.invoiceAppBar(
          context,
          title: 'Share Invoice',
          titleStyle: textTheme.titleLarge
              ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
          centerTitle: false,
        ),
        body: BlocBuilder<InvoiceViewerCubit, InvoiceState>(
          builder: (context, state) {
            if (state is InvoiceLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (state is InvoiceError) {
              return Center(
                child: KErrorScreen(
                  state.error,
                  () => cubit.getInvoice(invoice.id),
                ),
              );
            }

            if (state is InvoiceLoaded) {
              // Initialize PDF controller if not already done
              if (_pdfController == null &&
                  state.invoiceFile.file?.path != null) {
                _pdfController = PdfController(
                  document: PdfDocument.openFile(state.invoiceFile.file!.path),
                );
              }

              if (_pdfController != null) {
                return Column(
                  children: [
                    // PDF View takes most of the space
                    Expanded(
                      child: PdfView(
                        controller: _pdfController!,
                      ),
                    ),
                    // Share options at the bottom
                    _buildBottomShareOptions(state.invoiceFile),
                  ],
                );
              }
            }

            return const Center(
              child: Text('Unable to load invoice'),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBottomShareOptions(InvoiceFile invoiceFile) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, -2),
            blurRadius: 8,
            color: Colors.black.withValues(alpha: 0.1),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (whatsappInstalled) ...[
                _buildShareOption(
                  icon: 'assets/svgs/invoice/whatsapp.svg',
                  title: 'WhatsApp',
                  onTap: () {
                    if (invoiceFile.file?.path != null) {
                      final xFile = XFile(invoiceFile.file!.path);
                      shareWhatsappPlus.shareFile(xFile);
                    }
                  },
                ),
                const SizedBox(height: 12),
              ],
              _buildShareOption(
                icon: 'assets/svgs/invoice/copy.svg',
                title: 'Share',
                onTap: () {
                  if (invoiceFile.file?.path != null) {
                    final RenderBox box =
                        context.findRenderObject() as RenderBox;
                    Share.shareXFiles([
                      XFile(
                        invoiceFile.file!.path,
                        mimeType: 'application/pdf',
                      )
                    ],
                        subject: 'Invoice',
                        sharePositionOrigin:
                            box.localToGlobal(Offset.zero) & box.size);
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShareOption({
    required String icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: const Color(0xFFE5E7EB),
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                SvgPicture.asset(
                  icon,
                  height: 24,
                  width: 24,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.bodyLarge?.copyWith(
                      color: const Color(0xFF1F2937),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Icon(
                  Icons.chevron_right,
                  color: Color(0xFFE5E7EB),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ShareInvoiceScreenArgs {
  final CustomerInvoice invoice;
  final bool resetInvoiceFields;
  ShareInvoiceScreenArgs({
    required this.invoice,
    this.resetInvoiceFields = true,
  });
}
