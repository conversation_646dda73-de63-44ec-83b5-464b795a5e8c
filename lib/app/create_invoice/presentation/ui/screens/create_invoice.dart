import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:shop/app/authentication/presentation/logic/bloc/user_cubit.dart';
import 'package:shop/app/create_invoice/data/model/customer.dart';
import 'package:shop/app/create_invoice/data/model/shipping_cost.dart';
import 'package:shop/app/create_invoice/domain/params/create_invoice_params.dart';
import 'package:shop/app/create_invoice/presentation/logic/account_cubit/account_cubit_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/customer_cubit/customer_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/item_cubit/item_cubit_cubit.dart';
import 'package:shop/app/create_invoice/presentation/logic/selected_account_cubit.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/bank_account_card.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/customer_card.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/footer_button_widget.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/invoice_item_widget.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/k_tooltip.dart';
import 'package:shop/app/create_invoice/presentation/ui/widgets/shipping_cost.dart';
import 'package:shop/app/wallet_transfer/data/models/wallet_bank.dart';
import 'package:shop/route_constants.dart';
import 'package:shop/src/components/src/buttons/src/k_button_primary.dart';
import 'package:shop/src/components/src/toast/snackbar.dart';
import 'package:shop/src/components/src/utils/listenable/value_listenable_2.dart';
import 'package:shop/src/components/src/widgets/currency_item/index.dart';
import 'package:shop/src/components/src/widgets/shop_app_bar.dart';
import 'package:shop/src/res/assets/assets.dart';
import 'package:shop/src/res/values/app_values/app_values.dart';
import 'package:td_flutter_src/td_flutter_src.dart';

import '../widgets/colors.dart';

final shipping = ValueNotifier<Shipping?>(null);

class CreateInvoice extends StatefulWidget {
  const CreateInvoice({super.key});

  @override
  State<CreateInvoice> createState() => _CreateInvoiceState();
}

class _CreateInvoiceState extends State<CreateInvoice> {
  final _accountsKey = GlobalKey();
  final _subtotal = ValueNotifier<num>(0);

  @override
  void initState() {
    super.initState();
    context.read<AccountCubit>().fetchAccounts();
    resetCreateInvoiceFields(context);
  }

  final format = NumberFormat.compact();
  WalletBank? _selectedAccount;
  Customer? _selectedCustomer;

  final itemWidgetKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final customerCubit = context.read<CustomerCubit>();
    final itemsCubit = context.read<ItemCubit>();
    final accountCubit = context.read<SelectedAccountCubit>();

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (_, __) async {
        if (context.canPop()) {
          context.pop();
        } else {
          context.goNamed(HomePath);
        }
      },
      child: Scaffold(
        backgroundColor: backgroundColor,
        appBar: ShopAppBar.invoiceAppBar(
          context,
          title: 'Create Invoice',
          titleStyle: textTheme.titleLarge
              ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18),
          centerTitle: false,
          actions: [
            TextButton.icon(
              onPressed: () {
                context.pushNamed(PastInvoicesPath, extra: false);
              },
              icon: SvgPicture.asset(kSvgHistory),
              label: Text("Past Invoices",
                  style: theme.textTheme.bodyMedium
                      ?.copyWith(color: primaryColor)),
            ),
          ],
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: defaultHorizontalContentPadding,
                vertical: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  BlocConsumer<CustomerCubit, CustomerState>(
                    bloc: customerCubit,
                    listener: (context, state) {
                      if (customerCubit.selectedCustomer != null) {
                        setState(() {
                          _selectedCustomer = customerCubit.selectedCustomer;
                        });
                      }
                    },
                    builder: (context, state) {
                      final selectedCustomer = customerCubit.selectedCustomer;
                      final selected = selectedCustomer != null;
                      return InvoiceCard(
                        title: "Customer Information",
                        onChanged: selected
                            ? () {
                                context.pushNamed(SelectCustomerPath);
                              }
                            : null,
                        child: selected
                            ? CustomerCard(selectedCustomer)
                            : _buildSelectionBox(
                                selected: selected,
                                svgPath: kSvgPersonAddIcon,
                                text: "Select Customer",
                                onTap: () {
                                  context.pushNamed(SelectCustomerPath);
                                }, // Handle customer selection
                              ),
                      );
                    },
                  ),
                  const YMargin(24),
                  InvoiceCard(
                    title: "Items",
                    items: BlocConsumer<ItemCubit, ItemState>(
                      bloc: itemsCubit,
                      listener: (context, state) {
                        if (state is ManageItems) {
                          final sum = state.items.fold(
                              0.0,
                              (sum, item) =>
                                  sum + (item.price * item.quantity));
                          _subtotal.value = sum;
                        }
                      },
                      builder: (context, state) {
                        if ((state is ManageItems) && state.items.isNotEmpty) {
                          return ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: state.items.length,
                            itemBuilder: (context, index) {
                              final item =
                                  state.items[index].copyWith(index: index);
                              return InvoiceItemWidget(
                                item: item,
                                index: index,
                                isLast: index == state.items.length - 1,
                                onTap: () {
                                  context.pushNamed(AddItemPath, extra: item);
                                },
                                onDelete: () {
                                  itemsCubit
                                      .removeInvoiceItem(state.items[index]);
                                },
                              );
                            },
                          );
                        } else {
                          return const Offstage();
                        }
                      },
                    ),
                    child: _buildDashedButton(
                      svgPath: kSvgItemAddIcon,
                      text: "Add Item",
                      onTap: () {
                        context.pushNamed(AddItemPath);
                      },
                    ),
                  ),
                  const YMargin(24),
                  InvoiceCard(
                    title: "Payment Details",
                    child: _buildPaymentDetails(),
                  ),
                  const YMargin(24),
                  BlocConsumer<SelectedAccountCubit, WalletBank?>(
                    bloc: accountCubit,
                    listener: (context, state) {
                      setState(() {
                        _selectedAccount = state;
                      });
                    },
                    builder: (context, state) {
                      final hasSelectedBank = state != null;
                      return InvoiceCard(
                        key: _accountsKey,
                        title: "Receiving Account",
                        onChanged: hasSelectedBank
                            ? () {
                                context.pushNamed(PaymentMethodPath);
                              }
                            : null,
                        child: hasSelectedBank
                            ? BankAccountCard(state)
                            : _buildSelectionBox(
                                svgPath: kSvgBankSelect,
                                text: "Select Bank Account",
                                selected: state != null,
                                postfix: true,
                                onTap: () {
                                  context.pushNamed(PaymentMethodPath);
                                }, // Handle account selection
                              ),
                      );
                    },
                  ),
                  const YMargin(16),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: FooterButtonWidget(
          child: ValueListenableBuilder<num>(
            valueListenable: _subtotal,
            builder: (context, subtotal, _) {
              final disabled = subtotal <= 0 ||
                  _selectedAccount == null ||
                  _selectedCustomer == null;
              return KButtonPrimary(
                disabled: disabled,
                disabledColor: const Color(0xFFD1D5DB),
                textColor: disabled ? btnTextColor : Colors.white,
                elevation: 0,
                onTap: () async {
                  if (customerCubit.selectedCustomer?.id == null ||
                      customerCubit.selectedCustomer == null ||
                      customerCubit.selectedCustomer!.id!.isEmpty) {
                    SnackBarHelper.error(
                        'Please add customer details', context);
                    return;
                  }

                  if (itemsCubit.items.isEmpty) {
                    SnackBarHelper.error('Please add invoice item(s)', context);
                    return;
                  }

                  if (accountCubit.state?.id == null ||
                      accountCubit.state!.id.isEmpty) {
                    SnackBarHelper.error('Please add account details', context);
                    return;
                  }

                  final params = CreateInvoiceParams(
                    items: itemsCubit.items,
                    bankAccountId: _selectedAccount!.id,
                    customerId: customerCubit.selectedCustomer!.id!,
                    shipping: shipping.value,
                  );

                  context.pushNamed(InvoiceReviewPath, extra: params);
                },
                text: 'Review Invoice',
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildSelectionBox({
    required String svgPath,
    required String text,
    required VoidCallback onTap,
    bool? postfix = false,
    bool? selected = false,
  }) {
    final style = Theme.of(context).textTheme.bodyLarge;

    return InkWell(
      onTap: onTap,
      child: Container(
        height: 50,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: borderColor),
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              svgPath,
              color: selected! ? const Color(0xFF4B5563) : null,
            ),
            const XMargin(10),
            Expanded(
                child: Text(text,
                    style: selected
                        ? style
                        : style?.copyWith(
                            color: const Color(0xFF4B5563),
                            fontWeight: FontWeight.w400))),
            if (postfix!)
              const Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  Widget _buildDashedButton(
      {required String svgPath,
      required String text,
      required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        // padding: EdgeInsets.all(16),
        height: 42,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: borderColor, style: BorderStyle.solid),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(svgPath),
            const XMargin(10),
            Text(
              text,
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.copyWith(color: labelColor, fontWeight: FontWeight.w400),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetails() {
    return Column(
      children: [
        ValueListenableBuilder<num>(
          valueListenable: _subtotal,
          builder: (context, subtotal, _) {
            return _buildPaymentRow("Subtotal", subtotal);
          },
        ),
        const YMargin(16),
        ValueListenableBuilder<Shipping?>(
            valueListenable: shipping,
            builder: (context, data, _) {
              return _buildPaymentRow("Shipping Cost", data?.amount ?? 0,
                  child: GestureDetector(
                      onTap: () {
                        return ShippingCostWidget.open(
                          context,
                          initial: data,
                          onSave: (data) => shipping.value = data,
                        );
                      },
                      child: SvgPicture.asset(kSvgEditShippingIcon)));
            }),
        const YMargin(16),
        const Divider(color: borderColor),
        const YMargin(12),
        ValueListenableBuilder2<num, Shipping?>(
          _subtotal,
          shipping,
          builder: (context, subtotal, data, _) {
            //?TODO: add section for shipping note
            final total = subtotal + (data?.amount ?? 0);
            return _buildPaymentRow("Total", total,
                style: Theme.of(context)
                    .textTheme
                    .bodyLarge
                    ?.copyWith(fontWeight: FontWeight.w600));
          },
        ),
      ],
    );
  }

  Widget _buildPaymentRow(String label, num value,
      {Widget? child, TextStyle? style}) {
    final textTheme = Theme.of(context).textTheme;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: Row(
            children: [
              Text(label,
                  style: style ??
                      textTheme.bodyMedium
                          ?.copyWith(color: const Color(0xFF4B5563))),
              if (child != null) ...[
                const XMargin(2),
                child,
              ],
            ],
          ),
        ),
        Expanded(
          child: IntrinsicWidth(
            child: KTooltip(
              message: CurrencyItem.value(
                  context, value, UserCubit.instance!.currencyCode),
              child: Text(
                textAlign: TextAlign.end,
                CurrencyItem.value(
                    context, value, UserCubit.instance!.currencyCode),
                overflow: TextOverflow.ellipsis,
                style: style ??
                    textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF1F2937)),
              ),
            ),
          ),
        )
      ],
    );
  }
}

class InvoiceCard extends StatelessWidget {
  const InvoiceCard(
      {super.key, this.title, this.child, this.items, this.onChanged});

  final String? title;
  final Widget? child;
  final Widget? items;
  final VoidCallback? onChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      constraints: const BoxConstraints(
        minHeight: 50,
        minWidth: double.maxFinite,
      ),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title ?? '',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              if (onChanged != null)
                TextButton(
                  onPressed: onChanged,
                  child: Text(
                    'Change',
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.copyWith(color: primaryColor),
                  ),
                )
            ],
          ),
          if (items != null) ...[
            const YMargin(10),
            items!,
          ],
          if (child != null) ...[
            const YMargin(10),
            child!,
          ],
        ],
      ),
    );
  }
}
